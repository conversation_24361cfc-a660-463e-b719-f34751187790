import type { RouteRecordRaw } from 'vue-router';
import { useAuthPcStore } from '@/store/auth-pc';

/**
 * 所有受权限控制的动态路由
 * 父路由 name: baseHome
 */
const dynamicRoutes: any[] = [
  // portal-mgr
  // 门户宣传图管理
  {
    path: 'portal-mgr/portal-banner',
    name: 'pcPortalMgrPortalBanner',
    component: () => import('@/views/pc-uc/portal-mgr/portal-banner/index.vue'),
  },
  // 学会动态管理
  {
    path: 'portal-mgr/news',
    name: 'pcPortalMgrNews',
    component: () => import('@/views/pc-uc/portal-mgr/news/index.vue'),
  },
  // 通知公告管理
  {
    path: 'portal-mgr/notices',
    name: 'pcPortalMgrNotices',
    component: () => import('@/views/pc-uc/portal-mgr/notices/index.vue'),
  },
  // 学会资料上传
  // 学术期刊
  {
    path: 'portal-mgr/document-academic-journal',
    name: 'pcPortalMgrDocumentAcademicJournal',
    component: () => import('@/views/pc-uc/portal-mgr/document/index.vue'),
  },
  // 研究报告
  {
    path: 'portal-mgr/document-research-report',
    name: 'pcPortalMgrDocumentResearchReport',
    component: () => import('@/views/pc-uc/portal-mgr/document/index.vue'),
  },
  // 会员申请资料
  {
    path: 'portal-mgr/document-academic-database',
    name: 'pcPortalMgrDocumentAcademicDatabase',
    component: () => import('@/views/pc-uc/portal-mgr/document/index.vue'),
  },
  // 培训资料
  {
    path: 'portal-mgr/document-teaching-resources',
    name: 'pcPortalMgrDocumentTeachingResources',
    component: () => import('@/views/pc-uc/portal-mgr/document/index.vue'),
  },
  // 相关链接管理
  {
    path: 'portal-mgr/relateLink',
    name: 'pcPortalMgrRelateLink',
    component: () => import('@/views/pc-uc/portal-mgr/relateLink/index.vue'),
  },
  // 学会简介
  {
    path: 'portal-mgr/introduction',
    name: 'pcPortalMgrIntroduction',
    component: () => import('@/views/pc-uc/portal-mgr/introduction/index.vue'),
  },
  // 学会领导 - zzjg-1
  {
    path: 'portal-mgr/leader',
    name: 'pcPortalMgrLeader',
    component: () => import('@/views/pc-uc/portal-mgr/leader/index.vue'),
  },
  // 办事机构 - zzjg-2
  {
    path: 'portal-mgr/office',
    name: 'pcPortalMgrOffice',
    component: () => import('@/views/pc-uc/portal-mgr/office/index.vue'),
  },
  // 分支机构 - zzjg-3
  {
    path: 'portal-mgr/branch',
    name: 'pcPortalMgrBranch',
    component: () => import('@/views/pc-uc/portal-mgr/branch/index.vue'),
  },
  // 理事单位 - zzjg-4
  {
    path: 'portal-mgr/unit',
    name: 'pcPortalMgrUnit',
    component: () => import('@/views/pc-uc/portal-mgr/unit/index.vue'),
  },
  // 学会章程
  {
    path: 'portal-mgr/constitution',
    name: 'pcPortalMgrConstitution',
    component: () => import('@/views/pc-uc/portal-mgr/constitution/index.vue'),
  },

  // biz-sys-mgr
  {
    path: 'biz-sys-mgr/sign-info',
    name: 'pcBizSysMgrSignInfo',
    component: () => import('@/views/pc-uc/biz-sys-mgr/sign-info/index.vue'),
  },
  {
    path: 'biz-sys-mgr/sign-info-detail',
    name: 'pcBizSysMgrSignInfoDetail',
    component: () => import('@/views/pc-uc/biz-sys-mgr/sign-info/detail.vue'),
    meta: { includeTab: 'pcSignInfo' },
  },
  {
    path: 'biz-sys-mgr/hotel-info',
    name: 'pcBizSysMgrHotelInfo',
    component: () => import('@/views/pc-uc/biz-sys-mgr/hotel-info/index.vue'),
  },
  {
    path: 'biz-sys-mgr/person-mgr',
    name: 'pcBizSysMgrPersonMgr',
    component: () => import('@/views/pc-uc/biz-sys-mgr/person-mgr/index.vue'),
  },

  // base-info-mgr
  {
    path: 'base-info-mgr/role',
    name: 'pcBaseInfoMgrRole',
    component: () => import('@/views/pc-uc/base-info-mgr/role/index.vue'),
  },
  {
    path: 'base-info-mgr/user',
    name: 'pcBaseInfoMgrUser',
    component: () => import('@/views/pc-uc/base-info-mgr/user/index.vue'),
  },
  {
    path: 'base-info-mgr/org',
    name: 'pcBaseInfoMgrOrg',
    component: () => import('@/views/pc-uc/base-info-mgr/org/index.vue'),
  },
];

// 有权限的动态路由
const permsRouteList: RouteRecordRaw[] = [];

/**
 * 获取有权限的动态路由
 * path或path:childPath 如果在permsList权限表存在 则说明有权限
 */
export async function getPermsRouteList() {
  const resourceVoList = useAuthPcStore().userInfo.resourceVoList || [];

  // Map 表以 path 为 key
  const routeMap = new Map(dynamicRoutes.map((route) => [route.path, route]));

  // 遍历权限列表，直接从 Map 中查找对应路由
  resourceVoList.forEach((item: any) => {
    const route = routeMap.get(item.resUrl);
    if (route) {
      permsRouteList.push(route);
    }
  });

  return permsRouteList;
}

/**
 * 获取所有子路由
 * @param path 父路由路径
 */
export function getCurrentTabPermsRoutes(path: string) {
  for (const pRoute of permsRouteList) {
    if (path === pRoute.path) {
      return pRoute.children || [];
    }
  }

  return [];
}
