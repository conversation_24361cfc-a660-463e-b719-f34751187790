/**
 * 公共类
 */

:root {
  --header-height: 64px;

  --com-border-radius: 4px;
  --com-border: unset;
  --com-box-shadow: unset;
  --com-box-bg: var(--skin-bg3);
  --com-container-bg: var(--skin-bg3);
  --com-container-shadow: unset;
  --com-primary-color: var(--skin-c1);
  --com-pc-portal-primary-color: var(--skin-c6);
  --com-pc-portal-content-width: 1400px;
}

body {
  font-size: 14px;
  background: var(--skin-bg2);
}

.com-header {
  background: var(--com-primary-color);
  color: var(--skin-t1);
  width: 100%;
  height: var(--header-height);
  padding: 0 24px;
}

/* 容器背景 */
.com-container-bg {
  background: var(--com-container-bg);
}

.com-container-border {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-container-shadow);
}

/* 通用容器 */
.com-container {
  @extend .com-container-bg;
  @extend .com-container-border;
}

.com-pc-portal-container {
  width: var(--com-pc-portal-content-width);
  padding: 20px 0;
  margin: 0 auto;
}

.com-pc-portal-empty {
  position: absolute;
  top: calc(50% - 36px);
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 基础容器尺寸、颜色 private */
._container-base {
  position: relative;
  height: calc(100vh - var(--header-height) - 24px - 24px);
  margin: 24px 24px 0;
  color: #000;
  overflow: hidden;
}

/* 表格容器外层 */
.com-table-wrap {
  @extend ._container-base;
  display: grid;
  grid-template-rows: auto 1fr;
}

/* 表格容器 */
.com-table-container {
  @extend .com-container-bg;
  @extend .com-container-border;
  padding: 24px 24px 16px;
}

.com-table-filter {
  @extend .com-container-bg;
  @extend .com-container-border;
  padding: 24px;
}

.com-table-filter-nb {
  @extend .com-container-bg;
  @extend .com-container-border;
  border-radius: unset;
  padding: 24px;
}

// 只有底部带圆角 borer-left-right,
.com-table-filter-blr {
  @extend .com-container-bg;
  @extend .com-container-border;
  border-top-left-radius: unset;
  border-top-right-radius: unset;
  padding: 24px;
}


/* Box盒子（比container小的模块） */

.com-box-bg {
  background: var(--com-box-bg);
}

.com-box-border {
  border: var(--com-border);
  border-radius: var(--com-border-radius);
  box-shadow: var(--com-box-shadow);
}

.com-box {
  @extend .com-box-bg;
  @extend .com-box-border;
}

.com-action-button {
  --n-text-color: var(--com-primary-color) !important;
}

/* com-g-x-x -> */

.com-g-row-aa {
  display: grid;
  grid-template-rows: auto auto;
  grid-template-columns: minmax(0, 1fr);
}

.com-g-row-a1 {
  display: grid;
  grid-template-rows: auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.com-g-row-aa1 {
  display: grid;
  grid-template-rows: auto auto 1fr;
  grid-template-columns: minmax(0, 1fr);
}

.com-g-row-full {
  grid-template-rows: 100%;
}

.com-g-col-a1 {
  display: grid;
  grid-template-columns: auto 1fr;
}

.com-g-col-1a {
  display: grid;
  grid-template-columns: 1fr auto;
}

.com-g-col-full {
  grid-template-columns: 100%;
}

/* com-g-x-x  <- */

.com-empty {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  justify-items: center;
}

.com-batch-remove-button {
  position: absolute !important;
  bottom: 16px;
  left: 24px;
}


@mixin autofill($color, $theme) {
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    -webkit-background-clip: text;
    -webkit-text-fill-color: $color;
    color-scheme: $theme;
  }
}

.com-autofill-none-dark {
  @include autofill(rgba(255, 255, 255, 0.9), dark);
}

.com-autofill-none-light {
  @include autofill(rgba(0, 0, 0, 0.9), light);
}

.light {
  .com-autofill-none {
    @extend .com-autofill-none-dark;
  }
}

.dark {
  .com-autofill-none {
    @extend .com-autofill-none-light;
  }
}

.com-pc-portal-container-time{
   font-size: 14px;
  border-bottom: #ddd solid 1px;
  padding-bottom: 30px;
  margin-bottom: 20px;
  text-align: center;
  color: #999999ff;
}