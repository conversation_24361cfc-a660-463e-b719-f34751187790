export default {
  sus: {
    getOpenidByCode: '/pay/getOpenidByCode', // 获取用户openID
    prepay: '/pay/prepay', // 下单
    getOrdeStatus: '/pay/getOrdeStatus', // 获取订单状态(0：待支付，1支付成功，2，支付失败)
    queryPayRecord: '/pay/queryPayRecord', // 查询支付订单
    cancelPrepay: '/pay/cancelPrepay', // 取消下单
  },
  dcs: {
    fileUpload: '/common/v1/fileUpload', // 文件上传
    queryDictDataByType: '/common/v1/queryDictDataByType', // 字典
    login: '/auth/v1/login', // 登录 todo
    resetPassword: '/auth/v1/resetPassword', // 重置密码
    updatePassword: '/auth/v1/updatePassword', // 修改密码

    /**
     * portal - 门户网站管理
     */
    // banner
    portalBannerSave: 'portal/banner/save',
    portalBannerDelete: 'portal/banner/delete',
    portalBannerAudit: 'portal/banner/review',
    portalBannerPageList: 'portal/banner/pageList',
    // introduction
    portalIntroductionSave: 'portal/introduction/save',
    portalIntroductionDelete: 'portal/introduction/delete',
    portalIntroductionAudit: 'portal/introduction/review',
    portalIntroductionPageList: 'portal/introduction/pageList',
    // leader
    portalLeaderSave: 'portal/org/leader/save',
    portalLeaderDelete: 'portal/org/leader/delete',
    portalLeaderAudit: 'portal/org/leader/review',
    portalLeaderPageList: 'portal/org/leader/pageList',
    // office
    portalOfficeSave: 'portal/org/office/save',
    portalOfficeDelete: 'portal/org/office/delete',
    portalOfficeAudit: 'portal/org/office/review',
    portalOfficePageList: 'portal/org/office/pageList',
    // branch
    portalBranchSave: 'portal/org/branch/save',
    portalBranchDelete: 'portal/org/branch/delete',
    portalBranchAudit: 'portal/org/branch/review',
    portalBranchPageList: 'portal/org/branch/pageList',
    // unit
    portalUnitSave: 'portal/org/unit/save',
    portalUnitDelete: 'portal/org/unit/delete',
    portalUnitAudit: 'portal/org/unit/review',
    portalUnitPageList: 'portal/org/unit/pageList',
    // constitution
    portalConstitutionSave: 'portal/constitution/save',
    portalConstitutionDelete: 'portal/constitution/delete',
    portalConstitutionAudit: 'portal/constitution/review',
    portalConstitutionPageList: 'portal/constitution/pageList',

    /**
     * bizSys - 业务系统管理
     */

    /**
     * baseInfo - 基础信息管理
     */
    baseInfoRoleDelete: 'baseInfo/role/delete',
    baseInfoRoleSave: 'baseInfo/role/save',
    baseInfoRoleEditPermission: '/baseInfo/role/editPermission',
    baseInfoRolePageList: 'baseInfo/role/pageList',
    baseInfoRoleQueryRoleSelect: '/baseInfo/role/queryRoleSelect', // 角色下拉选项
    baseInfoRoleQueryMenuList: '/baseInfo/role/queryMenuList', // 查询菜单

    baseInfoUserDelete: 'baseInfo/user/delete',
    baseInfoUserSave: 'baseInfo/user/save',
    baseInfoUserPageList: 'baseInfo/user/pageList',
    baseInfoUserUpdateUserStatus: '/baseInfo/user/updateUserStatus',

    baseInfoOrgSave: 'baseInfo/org/save',
    baseInfoOrgPageList: 'baseInfo/org/pageList',
    baseInfoOrgGetOrgTree: '/baseInfo/org/getOrgTree',

    /**
     * 门户
     */
    portalShowIndexShowBanner: '/portalShow/index/showBanner', // 门户宣传图展示
  },
};
