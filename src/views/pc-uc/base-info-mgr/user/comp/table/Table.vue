<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import { $dialog } from '@/common/shareContext';
import { ACTION, ACTION_LABEL, ACTION_TIP_CONTENT } from '../../../../common/constant.ts';
import { cols } from './columns.ts';
import { DataTableColumns, NButton } from 'naive-ui';
import { IObj } from '@/types';
import { IPageItem } from '../../type.ts';
import { pageList, postDelete, postResetPwd, postUpdateUserStatus } from '../../fetchData.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { VNode, ref, toRaw, h } from 'vue';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(pageList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => handleResetPwd(row),
        },
        { default: () => ACTION_LABEL.RESET_PASSWORD }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => handleChangeStatus(row, 0),
        },
        { default: () => ACTION_LABEL.DISABLE }
      ),
      row.accountStatus == 1,
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => handleChangeStatus(row, 1),
        },
        { default: () => ACTION_LABEL.ENABLE }
      ),
      row.accountStatus == 0,
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => handleDelete(row),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];

  return useActionDivider(acList);
}

function handleResetPwd(row: IObj<any>) {
  $dialog.info({
    title: '重置密码',
    content: '确认重置密码？',
    positiveText: '确定',
    onPositiveClick: async () => {
      postResetPwd({ userId: row.id }).then(() => {
        getTableData();
      });
    },
  });
}

function handleChangeStatus(row: IObj<any>, status: number) {
  $dialog.info({
    title: '变更账号状态',
    content: status == 0 ? '停用后该账号将无法登录，确认停用？' : '启用后该账号将允许登录，确认启用？',
    positiveText: '确定',
    onPositiveClick: async () => {
      postUpdateUserStatus({ userId: row.id, userStatus: status }).then(() => {
        getTableData();
      });
    },
  });
}

function handleDelete(row: IObj<any>) {
  $dialog.error({
    title: '删除',
    content: ACTION_TIP_CONTENT.DELETE,
    positiveText: '确定',
    onPositiveClick: async () => {
      postDelete({ userId: row.id }).then(() => {
        getTableData();
      });
    },
  });
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'PcBaseInfoMgrUserTable' });
</script>

<style module lang="scss"></style>
