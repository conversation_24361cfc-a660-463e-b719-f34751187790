<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="标题" path="bt">
      <n-input v-model:value="formData.bt" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="通知附件" path="tzfjFileId">
      <FileUpload
        :data="tzFileList"
        accept="application/pdf"
        :max="3"
        :size="60"
        tips="请上传PDF文件，大小在60M以内,最大上传3个附件。"
        @update="handleFileListChange"
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, ref, Ref, watch } from 'vue';
import { FormInst } from 'naive-ui';
import { postSave } from '../../fetchData.ts';
import { IActionData } from '../../type.ts';
import FileUpload from '@/components/upload/file.vue';

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const actionData = computed(() => currentAction.value.data);

const initForm = () => {
  return {
    id: isEdit.value ? actionData.value.id : undefined, // 主键
    bt: isEdit.value ? actionData.value.bt : '',
    xslx: actionData.value.xslx,
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules = {
  bt: { required: true, message: '请输入标题', trigger: 'blur' },
  tzfjFileId: {
    required: true,
    message: '请上传通知附件',
    trigger: 'blur',
    validator: (_: any, value: any) => {
      return !!tzfjFileId.value;
    },
  },
};
const tzFileList = computed(() => actionData.value.tzfjFileAttachmentList || []);
const tzFiles = ref<any[]>([]);
const tzfjFileId = computed(() => tzFiles.value.map((item) => item.id).join(','));

function handleShow() {
  tzFiles.value = [...tzFileList.value];
}

function handleFileListChange(list: any[]) {
  tzFiles.value = list.map((item) => item.res);
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value, {
          tzfjFileId: tzfjFileId.value, // 通知附件附件ID，多个用逗号隔开
        });

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcDocumentAsideModify' });
</script>

<style module lang="scss"></style>
