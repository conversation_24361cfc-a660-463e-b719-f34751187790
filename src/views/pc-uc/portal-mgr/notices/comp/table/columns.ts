import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '标题',
    key: 'bt',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '发布者',
    key: 'fbz',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '发布时间',
    key: 'fbsj',
    align: 'center',
    width: 200,
  },
  {
    title: '发布状态',
    key: 'fbztName',
    align: 'center',
  },
  {
    title: '审核人',
    key: 'shrName',
    align: 'center',
  },
  {
    title: '排序',
    key: 'px',
    align: 'center',
  },
];
