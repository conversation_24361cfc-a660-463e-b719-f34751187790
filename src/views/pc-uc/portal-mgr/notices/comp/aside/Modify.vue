<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="标题" path="bt">
      <n-input v-model:value="formData.bt" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="副标题" path="fbt">
      <n-input v-model:value="formData.fbt" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="通知类型" path="tzlx">
      <n-radio-group v-model:value="formData.tzlx" name="tzlx">
        <n-space>
          <n-radio v-for="item in tzlxOpt" :key="item.id" :value="item.dictValue"> {{ item.dictLabel }} </n-radio>
        </n-space>
      </n-radio-group>
    </n-form-item>

    <n-form-item label="排序" path="px">
      <n-input-number v-model:value="formData.px" clearable :min="0" :max="9999" :show-button="false" class="!w-full" />
    </n-form-item>

    <n-form-item label="通知图" path="fileIds">
      <ImageUpload
        :data="tztFileList"
        :max="1"
        :size="20"
        tips="仅支持上传一张图片，图片大小限制20MB。"
        @update="handleFileListChange"
      />
    </n-form-item>

    <n-form-item label="通知附件" path="tzFileIds">
      <FileUpload
        :data="tzFileList"
        accept="application/pdf"
        :max="3"
        :size="60"
        tips="请上传PDF文件，大小在60M以内,最大上传3个附件。"
        @update="handleFileListChangeTz"
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, ref, Ref, watch } from 'vue';
import { FormInst } from 'naive-ui';
import { postSave } from '../../fetchData.ts';
import { IActionData } from '../../type.ts';
import ImageUpload from '@/components/upload/image.vue';
import FileUpload from '@/components/upload/file.vue';
import { $dict } from '@/views/pc/common/dict/dict.ts';

const props = defineProps({
  show: Boolean,
});

const { tzlxOpt } = $dict.useTzlx();
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const actionData = computed(() => currentAction.value.data);

const initForm = () => {
  return {
    id: isEdit.value ? actionData.value.id : undefined, // 主键
    bt: isEdit.value ? actionData.value.bt : '',
    px: isEdit.value ? actionData.value.px : null,
    tzlx: isEdit.value ? actionData.value.tzlx : null,
    fbt: isEdit.value ? actionData.value.fbt : null,
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules = {
  bt: { required: true, message: '请输入标题', trigger: 'blur' },
  fbt: { required: true, message: '请输入副标题', trigger: 'blur' },
  tzlx: { required: true, message: '请选择通知类型', trigger: 'blur' },
  px: { required: true, message: '请输入排序', trigger: 'blur', type: 'number' },
  fileIds: {
    required: true,
    message: '请上传通知图',
    trigger: 'blur',
    validator: (_: any) => {
      return !!fileIds.value;
    },
  },
  tzFileIds: {
    required: true,
    message: '请上传通知附件',
    trigger: 'blur',
    validator: (_: any) => {
      return !!tzFileIds.value;
    },
  },
};
const tztFileList = computed(() => actionData.value.tztFileAttachmentList || []);
const tzFileList = computed(() => actionData.value.tzfjFileAttachmentList || []);
const fileList = ref<any[]>([]);
const fileIds = computed(() => fileList.value.map((item) => item.id).join(','));

const tzFiles = ref<any[]>([]);
const tzFileIds = computed(() => tzFiles.value.map((item) => item.id).join(','));

function handleShowTz() {
  tzFiles.value = [...tzFileList.value];
}

function handleFileListChangeTz(list: any[]) {
  tzFiles.value = list.map((item) => item.res);
}

function handleShow() {
  fileList.value = [...tztFileList.value];
}

function handleFileListChange(list: any[]) {
  fileList.value = list.map((item) => item.res);
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value, {
          tztFileId: fileIds.value, // 通知图附件ID，多个用逗号隔开
          tzfjFileId: tzFileIds.value, // 通知附件附件ID，多个用逗号隔开
        });

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
      handleShowTz();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcNoticesAsideModify' });
</script>

<style module lang="scss"></style>
