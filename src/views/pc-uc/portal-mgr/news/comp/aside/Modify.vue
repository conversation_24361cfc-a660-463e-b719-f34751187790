<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="标题" path="bt">
      <n-input v-model:value="formData.bt" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="类型" path="lx">
      <n-select
        v-model:value="formData.lx"
        placeholder="请选择"
        label-field="dictLabel"
        value-field="dictValue"
        clearable
        :options="xhdtlxOpt"
      />
      <div class="ml-[10px]">
        <n-button @click="addLx" type="primary"> 新增类型 </n-button>
      </div>
    </n-form-item>

    <n-form-item label="封面图" path="fileIds">
      <ImageUpload
        :data="fmtFileList"
        :max="1"
        :size="20"
        tips="仅支持上传一张图片，图片大小限制20MB。"
        @update="handleFileListChange"
      />
    </n-form-item>

    <n-form-item label="正文" path="zw">
      <ComQuillEditor v-model="formData.zw" />
    </n-form-item>

    <n-form-item label="排序" path="px">
      <n-input-number v-model:value="formData.px" clearable :min="0" :max="9999" :show-button="false" class="!w-full" />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, h, inject, ref, Ref, watch } from 'vue';
import { FormInst, NInput } from 'naive-ui';
import { postAddDictByType, postSave } from '../../fetchData.ts';
import { IActionData } from '../../type.ts';
import ImageUpload from '@/components/upload/image.vue';
import ComQuillEditor from '@/components/editor/QuillEditor.vue';
import { $dialog } from '@/common/shareContext';
import PcNewsAsideCustomForm from './CustomForm.vue';
import { $dict } from '@/views/pc/common/dict/dict.ts';

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const actionData = computed(() => currentAction.value.data);
const { xhdtlxOpt } = $dict.useXhdtlx();

const initForm = () => {
  return {
    id: isEdit.value ? actionData.value.id : undefined, // 主键
    bt: isEdit.value ? actionData.value.bt : '',
    px: isEdit.value ? actionData.value.px : null,
    lx: isEdit.value ? actionData.value.lx : null,
    zw: isEdit.value ? actionData.value.zw : '',
  };
};

const formRef = ref(); // 用于新增类型的自定义表单组件引用
const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules = {
  bt: { required: true, message: '请输入标题', trigger: 'blur' },
  lx: { required: true, message: '请选择类型', trigger: 'blur' },
  zw: { required: true, message: '请输入正文', trigger: 'blur' },
  px: { required: true, message: '请输入排序', trigger: 'blur', type: 'number' },
  fileIds: {
    required: true,
    message: '请上传封面图',
    trigger: 'blur',
    validator: (_: any, value: any) => {
      return !!fileIds.value;
    },
  },
};
const fmtFileList = computed(() => actionData.value.fmtFileAttachmentList || []);
const fileList = ref<any[]>([]);
const fileIds = computed(() => fileList.value.map((item) => item.id).join(','));

function handleShow() {
  fileList.value = [...fmtFileList.value];
}

function handleFileListChange(list: any[]) {
  fileList.value = list.map((item) => item.res);
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value, {
          fmtFileId: fileIds.value, // 图附件ID，多个用逗号隔开
        });

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

function addLx() {
  $dialog.info({
    title: '新增学会动态类型',
    content: () => {
      return h(PcNewsAsideCustomForm, {
        ref: formRef, // 注意：这里将ref附加到组件上
      });
    },
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await formRef.value.validate(); // 触发表单验证
        addDtlx(formRef.value.formModel.dictLabel);
        return true;
      } catch (error) {
        return false;
      }
    },
  });
}

async function addDtlx(dictLabel: string) {
  const params = {
    dictType: 'dtlx',
    dictLabel,
  };
  await postAddDictByType(params);
  $dict.getSysDictByType('dtlx').then(({ data }) => {
    xhdtlxOpt.value = data;
  });
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcNewsAsideModify' });
</script>

<style module lang="scss"></style>
