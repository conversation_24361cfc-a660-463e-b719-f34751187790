<template>
  <ComPortalBox title="学会动态">
    <div class="contentBox1">
      <div v-for="item in listData" :key="item.id" class="contentBox1_list">
        <div class="header_img">
          <img :src="item.img" alt="动态图片" />
        </div>
        <div class="content">
          <p>{{ item.date }} · {{ item.gong }}</p>
          <p>{{ item.title }}</p>
          <p :title="item.content">{{ item.content }}</p>
          <div class="content_bottom">
            <span>阅读全文</span>
            <img src="../assets/jiantou.png" alt="箭头图标" />
          </div>
        </div>
      </div></div
  ></ComPortalBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';

const listData = ref([
  {
    id: 1,
    title: '城市基础设施生命线安全工程建设与发展论坛在沪成功召开',
    content:
      '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主',
    gong: '学会公告',
    img: 'https://www.gstanzer.com/upload/2025-06/174893945845706100.jpg',
    date: '2025-06-01',
  },
  {
    id: 2,
    title: '城市基础设施生命线安全工程建设与发展论坛在沪成功召开',
    content:
      '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主',
    gong: '学会公告',
    img: 'https://www.gstanzer.com/upload/2025-06/174893945845706100.jpg',
    date: '2025-06-02',
  },
  {
    id: 3,
    title: '城市基础设施生命线安全工程建设与发展论坛在沪成功召开',
    content:
      '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主',
    gong: '学会公告',
    img: 'https://www.gstanzer.com/upload/2025-06/174893945845706100.jpg',
    date: '2025-06-03',
  },
]);

defineOptions({ name: 'contentBox1Index' });
</script>

<style scoped lang="scss">
.contentBox1 {
  width: 100%;
  height: 440px;
  display: flex;
  justify-content: space-between;
  .contentBox1_list {
    width: 448px;
    height: 440px;
    border-radius: 8px;
    background-color: white;
    .header_img {
      img {
        width: 448px;
        height: 266px;
        border-radius: 8px 8px 0px 0px;
      }
    }
    .content {
      padding: 10px;
      p:nth-child(1) {
        font-size: 14px;
        color: #999;
      }
      p:nth-child(2) {
        margin-top: 10px;

        font-weight: 600;
        font-size: 18px;
        color: #000000;
      }
      p:nth-child(3) {
        margin-top: 10px;

        font-weight: 400;
        font-size: 14px;
        color: #666666;

        /* 添加以下属性实现溢出省略号 */
        white-space: nowrap; /* 禁止换行 */
        overflow: hidden; /* 隐藏溢出内容 */
        text-overflow: ellipsis; /* 显示省略号 */
        width: 100%; /* 或者指定一个固定宽度 */
      }
      .content_bottom {
        margin-top: 10px;
        display: flex;
        cursor: pointer;
        span {
          font-weight: 400;
          font-size: 16px;
          color: #0c87e5;
        }
        img {
          width: 20px;
          height: 20px;
          margin-top: 2px;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
