<template>
  <ComPortalBox :has-more="false">
    <div class="contentBox2">
      <div class="left">
        <ComPortalBox :has-bg="true">
          <template #title>
            <template v-for="(item, index) in spanList" :key="item.id">
              <span :class="activeIndex == index ? 'active' : 'sp'" @click="handleClick(index)">{{ item.name }}</span>
            </template>
          </template>
          <Table></Table>
        </ComPortalBox>
      </div>
      <div class="rigth">
        <ComPortalBox title="功能服务" :has-bg="true" :has-more="false">
          <Card></Card>
        </ComPortalBox>
      </div>
    </div>
  </ComPortalBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';
import Table from './table/index.vue';
import Card from './table/card.vue';

const activeIndex = ref(0);
const spanList = ref([
  { id: 1, name: '学会通知' },
  {
    id: 2,
    name: '分支机构通知',
  },
]);
const handleClick = (index: number) => {
  activeIndex.value = index;
};
defineOptions({ name: 'contentBox2Index' });
</script>

<style scoped lang="scss">
.contentBox2 {
  width: 100%;

  display: flex;
  justify-content: space-between;
  .left {
    width: 49%;
    .sp {
      display: inline-block;
      height: 50px;
      font-weight: 700;
      text-align: center;
      line-height: 50px;
      font-size: 25px;
      color: #333333;
      margin-right: 20px;
    }
    .active {
      display: inline-block;
      height: 50px;
      font-weight: 700;
      text-align: center;
      line-height: 50px;
      font-size: 26px;
      color: #0c87e5;
      margin-right: 20px;
      border-bottom: 3px solid #0c87e5;
    }
  }
  .rigth {
    width: 49%;
  }
}
</style>
