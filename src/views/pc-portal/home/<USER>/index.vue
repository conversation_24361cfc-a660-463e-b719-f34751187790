<template>
  <ComPortalBox title="近期会议" :has-bg="true">
    <n-scrollbar style="height: calc(478px - 126px)">
      <div class="tableDataDom" v-for="item in tableData" :key="item.id">
        <div class="left">
          <div class="img">
            <p>{{ item.day }}</p>
            <p>{{ item.month }}</p>
            <img src="../assets/jiao.png" class="jiao2" />
            <img src="../assets/jiao.png" class="jiao1" />
          </div>
        </div>
        <div class="content">
          <p>{{ item.title }}</p>
          <p>
            <span>{{ item.date }}</span
            >&nbsp;&nbsp;&nbsp;&nbsp;<span>{{ item.address }}</span>
          </p>
          <p>{{ item.content }}</p>
        </div>
        <div class="right">
          <n-button type="primary">立即报名</n-button>
        </div>
      </div>
    </n-scrollbar>
  </ComPortalBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';

const tableData = ref([
  {
    id: 1,
    title: '关于征集公共安全科学技术学会检测认证工作委员会委员的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
    date: '2025-06-01',
    address: '北京国际会议中心',
    day: '16',
    month: '6月',
  },
  {
    id: 2,
    title: '2025中国公共安全大会第二轮会议通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
    date: '2025-06-01',
    address: '北京国际会议中心',
    day: '16',
    month: '6月',
  },
  {
    id: 3,
    title: '关于征集中国公共安全大会承办单位的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
    date: '2025-06-01',
    address: '北京国际会议中心',
    day: '16',
    month: '6月',
  },
  {
    id: 4,
    title: '2024中国公共安全大会酒店预订的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
    date: '2025-06-01',
    address: '北京国际会议中心',
    day: '16',
    month: '6月',
  },
  {
    id: 5,
    title: '2024中国公共安全大会酒店预订的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
    date: '2025-06-01',
    address: '北京国际会议中心',
    day: '16',
    month: '6月',
  },
]);
defineOptions({ name: 'contentBox3Index' });
</script>

<style scoped lang="scss">
.tableDataDom {
  width: 98%;
  height: 110px;
  margin-top: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  // padding: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;

  .left {
    .img {
      width: 95px;
      height: 72px;
      // background-color: red;
      background-image: url('../assets/bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%; // 或者使用 background-size: cover;
      background-position: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      .jiao1 {
        width: 5px;
        height: 14px;
        position: absolute;
        top: -5px;
        left: 20px;
      }
      .jiao2 {
        width: 5px;
        height: 14px;
        position: absolute;
        top: -5px;
        right: 20px;
      }
      p:nth-child(1) {
        font-weight: 600;
        font-size: 24px;
        color: #0c87e5;
        line-height: 34px;
      }
      p:nth-child(2) {
        font-weight: 500;
        font-size: 14px;
        color: #0c87e5;
        line-height: 20px;
      }
    }
  }
  .content {
    flex: 1;
    margin: 0 30px;

    p {
      margin-bottom: 5px;
    }
    p:nth-child(1) {
      font-weight: 600;
      font-size: 18px;
      color: #000000;
    }
    p:nth-child(2) {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
    }
    p:nth-child(3) {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
    }
  }
  .right {
    img {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
