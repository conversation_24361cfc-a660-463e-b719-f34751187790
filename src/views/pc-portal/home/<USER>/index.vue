<template>
  <ComPortalBox title="学会动态">
    <div class="contentBox4">
      <div v-for="item in listData" :key="item.id" :class="item.id == 1 ? 'contentBox4_list' : 'contentBox4_list2'">
        <img :src="item.img" alt="动态图片" />
        <p>{{ item.title }}</p>
        <p>{{ item.content }}</p>
      </div>
    </div>
  </ComPortalBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';
import zl1 from '../assets/ziliao1.png';
import zl2 from '../assets/ziliao2.png';
import zl3 from '../assets/ziliao3.png';
import zl4 from '../assets/ziliao4.png';

const listData = ref([
  {
    id: 1,
    title: '会员申请资料',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    gong: '学会公告',
    img: zl1,
  },
  {
    id: 2,
    title: '研究报告',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    gong: '学会公告',
    img: zl2,
  },
  {
    id: 3,
    title: '学术期刊',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    gong: '学会公告',
    img: zl3,
  },
  {
    id: 4,
    title: '培训资料',
    content: '2025年4月18日-20日，以“新质赋能安全·科技引领未来”为主2025年4月18日-20日',
    gong: '学会公告',
    img: zl4,
  },
]);

defineOptions({ name: 'contentBox4Index' });
</script>

<style scoped lang="scss">
.contentBox4 {
  width: 100%;
  height: 252px;
  display: flex;
  justify-content: space-between;
  .contentBox4_list {
    width: 23%;
    height: 100%;
    border-radius: 8px;
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      width: 112px;
      height: 112px;
    }
    p {
      text-align: center;
    }
    p:nth-child(2) {
      font-weight: 600;
      font-size: 24px;
      color: #07417b;
    }
    p:nth-child(3) {
      width: 75%;
      margin-top: 10px;
      font-weight: 400;
      font-size: 14px;
      color: #505559;
    }
  }
  .contentBox4_list2 {
    width: 23%;
    height: 100%;
    border-radius: 8px;
    background: linear-gradient(180deg, rgba(207, 230, 252, 0.6) 0%, rgba(219, 236, 255, 0.6) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      width: 112px;
      height: 112px;
    }
    p {
      text-align: center;
    }
    p:nth-child(2) {
      font-weight: 600;
      font-size: 24px;
      color: #07417b;
    }
    p:nth-child(3) {
      width: 75%;
      margin-top: 10px;
      font-weight: 400;
      font-size: 14px;
      color: #505559;
    }
  }
}
</style>
