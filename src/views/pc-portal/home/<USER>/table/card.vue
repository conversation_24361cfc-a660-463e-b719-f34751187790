<template>
  <div class="cardDom">
    <div class="card" v-for="item in cardData" :key="item.id" :style="{ 'background-color': item.bg }">
      <img :src="item.img" alt="功能图标" />
      <p>{{ item.name }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import huiyi from '../../assets/huiyi.png';
import huiyuan from '../../assets/huiyuan.png';
import ruhui from '../../assets/ruhui.png';
import tuanbiao from '../../assets/tuanbiao.png';
import saishi from '../../assets/saishi.png';
import peixun from '../../assets/peixun.png';
import chengguo from '../../assets/chengguo.png';
import baojiang from '../../assets/baojiang.png';

const cardData = ref([
  { id: 1, name: '会议报名', img: huiyi, bg: '#F1F5FA' },
  { id: 2, name: '会员登陆', img: huiyuan, bg: '#F1F3FA' },
  { id: 3, name: '入会申请', img: ruhui, bg: '#F1F9FA' },
  { id: 4, name: '团标业务', img: tuanbiao, bg: '#EDFAF4' },
  { id: 5, name: '赛事报名', img: saishi, bg: '#F5FAF0' },
  { id: 6, name: '培训报名', img: peixun, bg: '#FAF7F1' },
  {
    id: 7,
    name: '成果评价',
    img: chengguo,
    bg: '#FAF4F1',
  },
  {
    id: 8,
    name: '报奖系统',
    img: baojiang,
    bg: '#FAF1F1',
  },
]);

defineOptions({ name: 'contentBox2CardIndex' });
</script>

<style scoped lang="scss">
.cardDom {
  width: 100%;
  margin: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: center;

  .card {
    width: 23%;
    border-radius: 6px;
    margin-right: 10px;
    height: 159px;
    display: flex;

    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    cursor: pointer;

    img {
      width: 68px;
      height: 68px;
      margin-bottom: 10px;
    }

    p {
      font-weight: 400;
      font-size: 16px;
      color: #000000;
      line-height: 22px;
      text-align: center;
    }
  }
}
</style>
