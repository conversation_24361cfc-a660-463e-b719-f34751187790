<template>
  <n-scrollbar style="height: calc(478px - 110px)">
    <div class="tableDataDom" v-for="item in tableData" :key="item.id">
      <div class="left">
        <img src="../../assets/xiaoxi.png" />
      </div>
      <div class="content">
        <p>{{ item.title }}</p>
        <p>{{ item.content }}</p>
      </div>
      <div class="right">
        <img src="../../assets/jiantou2.png" />
      </div>
    </div>
  </n-scrollbar>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const tableData = ref([
  {
    id: 1,
    title: '关于征集公共安全科学技术学会检测认证工作委员会委员的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
  },
  {
    id: 2,
    title: '2025中国公共安全大会第二轮会议通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
  },
  {
    id: 3,
    title: '关于征集中国公共安全大会承办单位的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
  },
  {
    id: 4,
    title: '2024中国公共安全大会酒店预订的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
  },
  {
    id: 5,
    title: '2024中国公共安全大会酒店预订的通知',
    content: '我们的研究团队在量子比特稳定性方面取得重要进展，相关成果已发表于《Nature》期刊。',
  },
]);

defineOptions({ name: 'contentBox2TableIndex' });
</script>

<style scoped lang="scss">
.tableDataDom {
  width: 98%;
  margin-top: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  // align-items: center;
  // padding: 10px;
  padding-bottom: 10px;
  // border-bottom: 1px solid #e0e0e0;

  .left {
    img {
      width: 42px;
      height: 42px;
    }
  }
  .content {
    flex: 1;
    margin: 0 10px;

    p:nth-child(1) {
      font-weight: 600;
      font-size: 18px;
      color: #000000;
      line-height: 25px;
    }
    p:nth-child(2) {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      margin-top: 10px;
      line-height: 20px;
    }
  }
  .right {
    img {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
