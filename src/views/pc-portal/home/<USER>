<template>
  <div>
    <Banner />

    <div class="com-pc-portal-container">
      <Xhdt />
      <Xhtz />
      <Jqhy class="!mt-[48px]" />
      <Zlxz class="!mt-[48px]" />
      <br />
      <br />
    </div>
  </div>
</template>

<script setup lang="ts">
import Banner from './banner/index.vue';
import Xhdt from './xhdt/index.vue';
import Xhtz from './xhtz/index.vue';
import Jqhy from './jqhy/index.vue';
import Zlxz from './zlxz/index.vue';
defineOptions({ name: 'PcPortalHomeIndex' });
</script>

<style scoped lang="scss">
.Jqhy {
  // margin-top: 48px;
}
</style>
