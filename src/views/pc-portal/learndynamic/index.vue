<template>
  <div>
    <div :class="$style.learndynamicheaderWrap">
      <div class="flex items-center justify-between h-[64px] w-[87.5rem] mx-auto">
        <div class="text-[#0C87E5FF] text-[20px] font-semibold">公共安全科学技术学会</div>
        <div>
          <n-tabs type="bar" @update:value="handleUpdateValue" v-model:value="tabName">
            <n-tab v-for="tab in tabs" :key="tab.name" :name="tab.name"> {{ tab.label }} </n-tab>
          </n-tabs>
        </div>
      </div>
      <div class="w-[87.5rem] mx-auto"><ComBread :data="breadData"></ComBread></div>
    </div>
    <div class="com-pc-portal-container"><ListIndex ref="tableRef"></ListIndex></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import ComBread from '@/views/pc-portal/common/comp/ComBread.vue';
import ListIndex from './comp/ListIndex.vue';
import { queryDictDataByTypeAPI } from './fetchData';

const tabName = ref('0');
const tableRef = ref();

// { name: '0', label: '学会动态' },
//   { name: '1', label: '科技创新' },
//   { name: '2', label: '学术研究' },
//   { name: '3', label: '学术交流' },
//   { name: '4', label: '学术推广' },
//   { name: '5', label: '国际交流' },

const tabs = ref<any[]>([]);
const breadData = ref<any>([{ name: '学会动态' }]);
function getTypeList() {
  queryDictDataByTypeAPI({ type: 'dtlx' }).then((res) => {
    console.log(res, 'p[p[p[pp]]]');
    tabs.value = res.data.map((item: any) => ({
      name: item.dictValue,
      label: item.dictLabel,
    }));
    breadData.value = [
      { name: '学会动态' },
      {
        name: tabs.value[+tabName.value].label,
      },
    ];
    tableRef.value.getTableDataWrap({ xslx: tabName.value });
    //   {
    //   name: tabs.value[+tabName.value].label,
    // },
  });
}
onMounted(() => {
  getTypeList();
});

function handleUpdateValue(val: string) {
  tabName.value = val;
  breadData.value = [
    { name: '学会动态' },
    {
      name: tabs.value[+tabName.value].label,
    },
  ];
  tableRef.value.getTableDataWrap({ xslx: tabName.value });
}

defineOptions({ name: 'LearnDynamicIndex' });
</script>

<style module lang="scss">
.learndynamicheaderWrap {
  width: 100%;
  height: 64px;
  background: #ffffff;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  border-radius: 0px 0px 0px 0px;
}
</style>
