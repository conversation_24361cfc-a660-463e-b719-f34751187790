<template>
  <div :class="$style['page-login']" class="com-g-row-a1 flex flex-col">
    <div class="flex items-center mt-[36px] ml-[40px]">
      <div :class="$style['logo']"></div>
      <div class="ml-[20px]">
        <div class="text-[#018982FF] text-[20.5px] font-semibold">公共安全科学技术学会</div>
        <div class="text-[#018982FF] text-[10px] font-normal">CHINA ASSOCATION FOR PUBLIC SAFETY</div>
      </div>
    </div>
    <div class="flex justify-end pt-[120px] pr-[208px]">
      <div :class="$style['login-form']">
        <LoginForm />
      </div>
    </div>

    <!-- <LoginForm /> -->
  </div>
</template>

<script lang="ts" setup>
import LoginForm from './LoginForm.vue';

defineOptions({ name: 'LoginIndex' });
</script>

<style module lang="scss">
.page-login {
  height: 100vh;
  background: url('./assets/bj.png') no-repeat center;
  background-size: cover;
}
.logo {
  width: 60px;
  height: 60px;
  background: url('@/assets/log.png') no-repeat center;
  background-size: contain;
}
.login-form {
  width: 422px;
  height: 420px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 16px 16px 16px 16px;
  border: 2px solid #ffffff;
}
</style>
