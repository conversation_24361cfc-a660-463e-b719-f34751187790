<template>
  <div :class="$style.introductionContent">
    <h2 :class="$style.title">学会简介</h2>

    <div :class="$style.mainContent">
      <!-- 文字内容区域 -->
      <div :class="$style.textContent">
        <!-- 右侧Logo区域 - 放在最开头以实现环绕效果 -->
        <div :class="$style.logoSection">
          <div :class="$style.logoContainer"></div>
        </div>

        <div :class="$style.description" v-html="introductionData.description"></div>
        <div :class="$style.content" v-html="introductionData.content"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

defineOptions({ name: 'IntroductionContent' });

const introductionData = ref({
  description: `
    <p>
      公共安全科学技术学会（以下简称学会）是由公共安全科学技术领域的专业科研、学者组织、人才队伍、管理服务机构和工作者等组成的学术团体，
      承务社会公共安全智库职能、学术引领、学会发展、技术应用和评估咨询等职责。
    </p>
  `,
  content: `
    <p>
      学会总部设在北京（公共安全部消防科学研究所），现有会员235名以内）负责学会管理工作，于2017年8月获得民政部民政部社会机构管理局允许
      注册成立。在《中国科协团体标准》（标准号2021年3月-1）学术项目组成立，承担其相关性业务领域管理。
    </p>
    <p>学会主要从事公共安全科学技术研究工作，业务范围包括：</p>
    <div class="business-scope">
      <div class="scope-item">
        <span class="font-medium">（一）</span>
        围绕公共安全科学技术行为基础理论、共性技术公共安全科学技术相关学术发展，制定相关标准规范，举办学术会议。
        致力于拓展学会发展；
      </div>
      <div class="scope-item">
        <span class="font-medium">（二）</span>
        参与公共安全科学技术相关标准制定学会工作，行政部门、司法行政部门的咨询服务，管理机构规划、实施和方法；
      </div>
      <div class="scope-item">
        <span class="font-medium">（三）</span>
        坚持公共安全科学技术学会市场前景相关，政务服务规范性，制定公共安全科学技术发展基础规联、科技成果获机械、
        基本公共基础设施设备管理和结构化管理建议和咨询建设建议行，意见制定，团队技术提供基础性和管理性服务，管理领域
        考察，基础通过，基础专业，基础会议，基础单位会、信息工业应用，基础网络基础和标准化主要业务；
      </div>
      <div class="scope-item">
        <span class="font-medium">（四）</span>
        培训、培训和市场法发展人才流程，注册服务教育管理培训，具体培训和社会教育培训过程综合教育、学术工作、
        科研和讲座社会流程管理过程培训提高公共安全科学技术管理教育工作程；
      </div>
      <div class="scope-item">
        <span class="font-medium">（五）</span>
        开展青年科学家发展管理基础服务，组织开展公共安全科学技术相关基础；
      </div>
      <div class="scope-item">
        <span class="font-medium">（六）</span>
        参与其他各类相关事业保障基础，营造公共民主综合培训考评标准收基础和报。
      </div>
    </div>
  `,
});
</script>

<style module lang="scss">
.introductionContent {
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 24px;
  }

  .mainContent {
    display: block;
  }

  .textContent {
    .logoSection {
      float: right;
      margin-left: 20px;
      margin-bottom: 20px;
      background: #fff;

      .logoContainer {
        width: 486px;
        height: 320px;
        padding: 43px 28px;
        text-align: center;
        background: url('../assets/content_bg.png') no-repeat center center;
      }

      .logoWrapper {
        width: 200px;
        height: 200px;
        margin: 0 auto 1rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .logo {
        width: 160px;
        height: 160px;
        object-fit: contain;
      }

      .logoTitle {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        text-align: center;
      }
    }

    .description {
      margin-bottom: 24px;

      p {
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        text-indent: 2em;
      }
    }

    .content {
      font-size: 16px;
      line-height: 1.8;
      color: #333;

      p {
        margin-bottom: 1rem;
        text-indent: 2em;
      }
    }

    .businessScope {
      margin-left: 1rem;

      .scopeItem {
        margin-bottom: 1rem;
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;

        span {
          flex-shrink: 0;
          width: 2em;
        }
      }
    }

    // v-html渲染的内容样式
    :global(.business-scope) {
      margin-left: 1rem;

      .scope-item {
        margin-bottom: 1rem;
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;

        span {
          flex-shrink: 0;
          width: 2em;
        }
      }
    }
  }
}
</style>
