<template>
  <div :class="$style.developmentHistory">
    <h2 :class="$style.title">发展历程</h2>

    <div :class="$style.timeline">
      <div v-for="(item, index) in timelineData" :key="item.id" :class="$style.timelineItem">
        <div :class="$style.timePoint">
          <div :class="$style.timeCircleW">
            <div :class="$style.timeCircleN">{{ index + 1 }}</div>
          </div>
          <div :class="$style.timeLine" v-if="index < timelineData.length - 1"></div>
        </div>

        <div :class="$style.timeContent">
          <div :class="$style.timeHeader">
            <span :class="$style.timeText">{{ item.year }}年{{ item.month }}月 - {{ item.title }}</span>
          </div>
          <div :class="$style.timeDescription" v-if="item.description">
            {{ item.description }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface ITimelineItem {
  id: string;
  year: string;
  month: string;
  title: string;
  description?: string;
  type?: 'milestone' | 'event' | 'achievement';
}

const timelineData = ref<ITimelineItem[]>([
  {
    id: '1',
    year: '2017',
    month: '3',
    title: '开始筹建',
    description: '2017年3月开始筹建工作，确定社会主体标准',
    type: 'milestone',
  },
  {
    id: '2',
    year: '2017',
    month: '7',
    title: '获授成立大会',
    description: '2017年7月获得民政部批准成立社会学会等学会',
    type: 'milestone',
  },
  {
    id: '3',
    year: '2017',
    month: '8',
    title: '学会成立登记',
    description: '2017年8月在民政部完成（挂靠2017年5月）民营学会获得政府法案合作规划和登记',
    type: 'achievement',
  },
  {
    id: '4',
    year: '2018',
    month: '',
    title: '首届理事会',
    description: '运营研发创新群体标准建设工作，许可建设中',
    type: 'event',
  },
  {
    id: '5',
    year: '2019',
    month: '',
    title: '快速发展',
    description: '基础建设设置相关，公共安全与新兴技术技术要素建设一，',
    type: 'achievement',
  },
]);

defineOptions({ name: 'DevelopmentHistory' });
</script>

<style module lang="scss">
.developmentHistory {
  margin-top: 30px;
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 32px;
  }

  .timeline {
    position: relative;
    max-width: 800px;
  }

  .timelineItem {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .timePoint {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 32px;
    position: relative;
    margin-top: 24px;

    .timeCircleW {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(12, 135, 229, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .timeCircleN {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #0c87e5;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 12px;
    }

    .timeLine {
      position: absolute;
      top: 38px;
      width: 2px;
      height: 107px;
      background: rgba(200, 214, 225, 0.5);
    }
  }

  .timeContent {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: -12px;
      top: 20px;
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-right: 8px solid white;
    }

    .timeHeader {
      margin-bottom: 12px;

      .timeText {
        font-size: 18px;
        font-weight: 600;
        color: #0c87e5;
      }
    }

    .timeDescription {
      font-size: 16px;
      line-height: 1.6;
      color: #666;
    }
  }
}
</style>
