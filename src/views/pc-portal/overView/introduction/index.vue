<template>
  <div :class="$style.introductionPage">
    <!-- 学会简介内容 -->
    <IntroductionContent />

    <!-- 发展历程内容 -->
    <DevelopmentHistory />
  </div>
</template>

<script setup lang="ts">
import IntroductionContent from './comp/IntroductionContent.vue';
import DevelopmentHistory from './comp/DevelopmentHistory.vue';

defineOptions({ name: 'IntroductionIndex' });
</script>

<style module lang="scss">
.introductionPage {
  width: 100%;
  // min-height: 600px;
}
</style>
