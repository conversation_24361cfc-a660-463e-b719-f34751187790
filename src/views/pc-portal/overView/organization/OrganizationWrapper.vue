<template>
  <div :class="$style.organizationWrapper">
    <!-- 组织机构内容区域 -->
    <OrganizationIndex :active-sub-tab="props.activeSubTab" />
  </div>
</template>

<script setup lang="ts">
import OrganizationIndex from './index.vue';

interface Props {
  activeSubTab?: string;
}

const props = withDefaults(defineProps<Props>(), {
  activeSubTab: 'leadership',
});

defineOptions({ name: 'OrganizationWrapper' });
</script>

<style module lang="scss">
.organizationWrapper {
  width: 100%;
}
</style>
