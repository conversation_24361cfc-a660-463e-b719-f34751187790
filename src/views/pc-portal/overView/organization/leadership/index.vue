<template>
  <div :class="$style.organizationLeadershipPage">
    <h2 :class="$style.title">理事会领导</h2>

    <!-- 领导成员网格 -->
    <div :class="$style.leadersGrid">
      <div v-for="leader in leadersData" :key="leader.id" :class="$style.leaderCard">
        <!-- 头像 -->
        <div :class="$style.avatar">
          <img :src="leader.avatar" :alt="leader.name" />
        </div>

        <div :class="$style.info">
          <div :class="$style.name">{{ leader.name }}</div>
          <div :class="$style.position">{{ leader.position }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface LeadershipMember {
  id: number;
  name: string;
  position: string;
  avatar: string;
}

const leadersData = ref<LeadershipMember[]>([
  {
    id: 1,
    name: '范维澄院士',
    position: '理事长',
    avatar: '/src/assets/avatars/fan.jpg',
  },
  {
    id: 2,
    name: '范维澄院士',
    position: '名誉理事长',
    avatar: '/src/assets/avatars/fan2.jpg',
  },
  {
    id: 3,
    name: '郭剑波院士',
    position: '副理事长',
    avatar: '/src/assets/avatars/guo.jpg',
  },
  {
    id: 4,
    name: '张来斌院士',
    position: '副理事长',
    avatar: '/src/assets/avatars/zhang.jpg',
  },
  {
    id: 5,
    name: '方泰教授',
    position: '副理事长',
    avatar: '/src/assets/avatars/fang.jpg',
  },
  {
    id: 6,
    name: '蒋军成教授',
    position: '副理事长',
    avatar: '/src/assets/avatars/jiang.jpg',
  },
  {
    id: 7,
    name: '王成主任',
    position: '副理事长',
    avatar: '/src/assets/avatars/wang.jpg',
  },
]);

defineOptions({ name: 'OrganizationLeadershipIndex' });
</script>

<style module lang="scss">
.organizationLeadershipPage {
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 2rem;
    text-align: left;
  }

  .leadersGrid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 28px;

    .leaderCard {
      width: 448px;
      height: 102px;
      padding-left: 25px;
      background: #fff;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      cursor: pointer;
      display: flex;
      align-items: center;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      }
      .avatar {
        width: 54px;
        height: 54px;
        border-radius: 50%;
        overflow: hidden;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 21px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .info {
        .name {
          font-size: 18px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 0.5rem;
        }
        .position {
          font-size: 14px;
          color: #2196f3;
          font-weight: 500;
          float: left;
        }
      }
    }
  }
}
</style>
