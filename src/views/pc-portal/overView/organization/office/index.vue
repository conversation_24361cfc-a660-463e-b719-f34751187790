<template>
  <div :class="$style.officePage">
    <h2 :class="$style.title">办事机构</h2>

    <div :class="$style.leadersGrid">
      <div v-for="leader in leadersData" :key="leader.id" :class="$style.leaderCard">
        <div :class="$style.name">{{ leader.name }}</div>
        <div :class="$style.position">{{ leader.position }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

interface LeadershipMember {
  id: number;
  name: string;
  position: string;
}

const leadersData = ref<LeadershipMember[]>([
  {
    id: 1,
    name: '范维澄院士',
    position: '理事长12321323213',
  },
  {
    id: 2,
    name: '范维澄院士',
    position: '名誉理事长312312312',
  },
  {
    id: 3,
    name: '郭剑波院士',
    position: '副理事长312313123',
  },
  {
    id: 4,
    name: '张来斌院士',
    position: '副理事长31231312',
  },
  {
    id: 5,
    name: '方泰教授',
    position: '副理事长3131312',
  },
  {
    id: 6,
    name: '蒋军成教授',
    position: '副理事长31312312',
  },
  {
    id: 7,
    name: '王成主任',
    position: '副理事长312312312',
  },
]);
defineOptions({ name: 'OfficeIndex' });
</script>

<style module lang="scss">
.officePage {
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 2rem;
    text-align: left;
  }

  .leadersGrid {
    background: #fff;
    .leaderCard {
      height: 102px;
      padding-left: 25px;
      text-align: center;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      margin-bottom: 10px;

      .name {
        font-size: 18px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 14px;
      }
      .position {
        font-family:
          Alibaba PuHuiTi 2,
          Alibaba PuHuiTi 20;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }
    }
  }
}
</style>
