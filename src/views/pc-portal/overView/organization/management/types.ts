/**
 * 理事单位相关类型定义
 */

/**
 * 理事单位类型枚举
 */
export enum DirectorUnitType {
  CHAIRMAN = 'chairman',
  VICE_CHAIRMAN = 'viceChairman',
  EXECUTIVE_DIRECTOR = 'executiveDirector',
  DIRECTOR = 'director',
}

/**
 * 理事单位数据接口
 */
export interface IDirectorUnit {
  /** 主键ID */
  id: string;
  /** 单位名称 */
  unitName: string;
  /** 单位类型 */
  unitType: DirectorUnitType;
  /** 排序 */
  sort?: number;
}

/**
 * 理事单位查询参数
 */
export interface IDirectorUnitQuery {
  unitType?: DirectorUnitType;
  unitName?: string;
  pageNo?: number;
  pageSize?: number;
}

/**
 * 理事单位分类菜单项
 */
export interface IDirectorUnitCategory {
  key: DirectorUnitType;
  label: string;
}
