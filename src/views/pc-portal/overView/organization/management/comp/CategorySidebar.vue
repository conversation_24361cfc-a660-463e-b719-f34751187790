<template>
  <div :class="$style.categorySidebar">
    <div
      v-for="category in categories"
      :key="category.key"
      :class="[$style.categoryItem, { [$style.active]: activeCategory === category.key }]"
      @click="handleCategoryClick(category.key)"
    >
      {{ category.label }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getDirectorUnitCategories } from '../fetchData';
import { DirectorUnitType } from '../types';
import type { IDirectorUnitCategory } from '../types';

defineOptions({ name: 'CategorySidebar' });

interface Props {
  activeCategory?: DirectorUnitType;
}

interface Emits {
  (e: 'categoryChange', category: DirectorUnitType): void;
}

const props = withDefaults(defineProps<Props>(), {
  activeCategory: DirectorUnitType.CHAIRMAN,
});

const emit = defineEmits<Emits>();

const categories = ref<IDirectorUnitCategory[]>([]);

/**
 * 处理分类点击
 */
const handleCategoryClick = (categoryKey: DirectorUnitType) => {
  emit('categoryChange', categoryKey);
};

onMounted(() => {
  categories.value = getDirectorUnitCategories();
});
</script>

<style module lang="scss">
.categorySidebar {
  width: 318px;
  background: #f8f9fa;
  padding: 22px;

  .categoryItem {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 18px;
    color: #212121;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    margin-bottom: 12px;

    &:hover {
      background: #e9ecef;
      color: #333;
    }

    &.active {
      background: #e0f2ff;
      color: #0c87e5;
      font-weight: 600;
      border: 1px solid #0c87e5;
    }
  }
}
</style>
