<template>
  <div :class="$style.unitListContainer">
    <div :class="$style.unitHeader">
      <span :class="$style.headerTitle">单位名称</span>
    </div>

    <div :class="$style.unitList" v-loading="loading">
      <div v-for="unit in unitList" :key="unit.id" :class="$style.unitItem">
        {{ unit.unitName }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { getDirectorUnitList } from '../fetchData';
import { DirectorUnitType } from '../types';
import type { IDirectorUnit } from '../types';

defineOptions({ name: 'UnitList' });

interface Props {
  activeCategory: DirectorUnitType;
}

const props = defineProps<Props>();

const loading = ref(false);
const unitList = ref<IDirectorUnit[]>([]);

const loadDirectorUnitData = async (unitType: DirectorUnitType) => {
  try {
    loading.value = true;
    const result = await getDirectorUnitList({ unitType });
    unitList.value = result.rows;
  } catch (error) {
    console.error('加载理事单位数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 监听分类变化，重新加载数据
watch(
  () => props.activeCategory,
  (newCategory) => {
    loadDirectorUnitData(newCategory);
  },
  { immediate: true }
);
</script>

<style module lang="scss">
.unitListContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 22px;

  .unitHeader {
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: #edf1f5;

    .headerTitle {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .unitList {
    flex: 1;
    padding: 0;
    max-height: 440px;
    overflow-y: auto;

    .unitItem {
      height: 60px;
      line-height: 60px;
      text-align: center;
      padding: 0 24px;
      font-size: 14px;
      color: #666;
      border-bottom: 1px solid #f0f0f0;
      transition: background 0.2s ease;

      &:hover {
        background: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

@media (max-width: 768px) {
  .unitListContainer {
    .unitList {
      max-height: 300px;
    }
  }
}
</style>
