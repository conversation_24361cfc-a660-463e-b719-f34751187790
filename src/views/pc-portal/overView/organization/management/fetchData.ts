/**
 * 理事单位相关接口服务
 */
import type { IPageRes } from '@/types';
import type { IDirectorUnit, IDirectorUnitQuery, IDirectorUnitCategory } from './types';
import { DirectorUnitType } from './types';

/**
 * 获取理事单位分类列表
 * @returns 理事单位分类列表
 */
export function getDirectorUnitCategories(): IDirectorUnitCategory[] {
  return [
    {
      key: DirectorUnitType.CHAIRMAN,
      label: '理事长单位',
    },
    {
      key: DirectorUnitType.VICE_CHAIRMAN,
      label: '副理事长单位',
    },
    {
      key: DirectorUnitType.EXECUTIVE_DIRECTOR,
      label: '常务理事单位',
    },
    {
      key: DirectorUnitType.DIRECTOR,
      label: '理事单位',
    },
  ];
}

/**
 * 获取理事单位列表
 * @param params 查询参数
 * @returns 理事单位列表
 */
export async function getDirectorUnitList(params?: IDirectorUnitQuery): Promise<IPageRes<IDirectorUnit>> {
  // TODO: 后续替换为真实接口调用

  // 模拟数据
  const allMockData: IDirectorUnit[] = [
    // 理事长单位
    {
      id: '1',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.CHAIRMAN,
      sort: 1,
    },
    // 副理事长单位
    {
      id: '2',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.VICE_CHAIRMAN,
      sort: 1,
    },
    {
      id: '3',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.VICE_CHAIRMAN,
      sort: 2,
    },
    {
      id: '4',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.VICE_CHAIRMAN,
      sort: 3,
    },
    // 常务理事单位
    {
      id: '5',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.EXECUTIVE_DIRECTOR,
      sort: 1,
    },
    {
      id: '6',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.EXECUTIVE_DIRECTOR,
      sort: 2,
    },
    {
      id: '7',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.EXECUTIVE_DIRECTOR,
      sort: 3,
    },
    {
      id: '8',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.EXECUTIVE_DIRECTOR,
      sort: 4,
    },
    // 理事单位
    {
      id: '9',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.DIRECTOR,
      sort: 1,
    },
    {
      id: '10',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.DIRECTOR,
      sort: 2,
    },
    {
      id: '11',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.DIRECTOR,
      sort: 3,
    },
    {
      id: '12',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.DIRECTOR,
      sort: 4,
    },
    {
      id: '13',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.DIRECTOR,
      sort: 5,
    },
    {
      id: '14',
      unitName: '中国矿业大学（北京）',
      unitType: DirectorUnitType.DIRECTOR,
      sort: 6,
    },
  ];

  // 根据单位类型过滤数据
  let filteredData = allMockData;
  if (params?.unitType) {
    filteredData = allMockData.filter((item) => item.unitType === params.unitType);
  }

  // 模拟异步请求
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        rows: filteredData,
        pageNo: params?.pageNo || 1,
        pageSize: params?.pageSize || 20,
        pages: 1,
        total: filteredData.length,
      });
    }, 300);
  });
}

/**
 * 获取理事单位详情
 * @param id 理事单位ID
 * @returns 理事单位详情
 */
export async function getDirectorUnitDetail(id: string): Promise<IDirectorUnit> {
  // TODO: 后续替换为真实接口调用

  // 模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id,
        unitName: '中国矿业大学（北京）',
        unitType: DirectorUnitType.CHAIRMAN,
      });
    }, 200);
  });
}
