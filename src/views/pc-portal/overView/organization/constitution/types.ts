/**
 * 分支机构相关类型定义
 */

/**
 * 分支机构数据接口
 */
export interface IBranchOffice {
  /** 主键ID */
  id: string;
  /** 组织管理部门 */
  organizationDepartment: string;
  /** 成立时间 */
  establishmentDate: string;
  /** 责任人 */
  responsiblePerson: string;
}

/**
 * 分支机构查询参数
 */
export interface IBranchOfficeQuery {
  /** 组织管理部门 */
  organizationDepartment?: string;
  /** 责任人 */
  responsiblePerson?: string;
  /** 页码 */
  pageNo?: number;
  /** 每页数量 */
  pageSize?: number;
}

/**
 * 分支机构表格列配置
 */
export interface IBranchOfficeColumn {
  key: string;
  title: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  ellipsis?: boolean;
}
