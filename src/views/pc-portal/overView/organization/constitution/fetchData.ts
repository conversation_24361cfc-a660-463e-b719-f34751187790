/**
 * 分支机构相关接口服务
 */
import type { IPageRes } from '@/types';
import type { IBranchOffice, IBranchOfficeQuery } from './types';

/**
 * 获取分支机构列表
 * @param params 查询参数
 * @returns 分支机构列表
 */
export async function getBranchOfficeList(params?: IBranchOfficeQuery): Promise<IPageRes<IBranchOffice>> {
  // TODO: 后续替换为真实接口调用

  // 模拟数据
  const mockData: IBranchOffice[] = [
    {
      id: '1',
      organizationDepartment: '应急管理专业委员会',
      establishmentDate: '2023-12-23',
      responsiblePerson: '李豆豆',
    },
    {
      id: '2',
      organizationDepartment: '应急管理专业委员会',
      establishmentDate: '2023-12-23',
      responsiblePerson: '李豆豆',
    },
    {
      id: '3',
      organizationDepartment: '应急管理专业委员会',
      establishmentDate: '2023-12-23',
      responsiblePerson: '李豆豆',
    },
    {
      id: '4',
      organizationDepartment: '应急管理专业委员会',
      establishmentDate: '2023-12-23',
      responsiblePerson: '李豆豆',
    },
    {
      id: '5',
      organizationDepartment: '应急管理专业委员会',
      establishmentDate: '2023-12-23',
      responsiblePerson: '李豆豆',
    },
    {
      id: '6',
      organizationDepartment: '应急管理专业委员会',
      establishmentDate: '2023-12-23',
      responsiblePerson: '李豆豆',
    },
  ];

  // 模拟异步请求
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        rows: mockData,
        pageNo: params?.pageNo || 1,
        pageSize: params?.pageSize || 10,
        pages: 1,
        total: mockData.length,
      });
    }, 500);
  });
}
