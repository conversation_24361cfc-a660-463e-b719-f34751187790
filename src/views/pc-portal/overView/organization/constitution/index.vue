<template>
  <div :class="$style.branchOfficePage">
    <h2 :class="$style.title">学会分支机构</h2>

    <div :class="$style.tableContainer">
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :max-height="400"
        :scroll-x="750"
        striped
        size="medium"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NDataTable } from 'naive-ui';
import { branchOfficeColumns } from './columns';
import { getBranchOfficeList } from './fetchData';
import type { IBranchOffice } from './types';

defineOptions({ name: 'BranchOfficeIndex' });

const loading = ref(false);
const tableData = ref<IBranchOffice[]>([]);
const columns = branchOfficeColumns;

/**
 * 加载分支机构数据
 */
const loadBranchOfficeData = async () => {
  try {
    loading.value = true;
    const result = await getBranchOfficeList();
    tableData.value = result.rows;
  } catch (error) {
    console.error('加载分支机构数据失败:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  loadBranchOfficeData();
});
</script>

<style module lang="scss">
.branchOfficePage {
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 32px;
    text-align: left;
  }

  .tableContainer {
    background-color: #fff;
    padding: 20px;
  }
}
</style>
