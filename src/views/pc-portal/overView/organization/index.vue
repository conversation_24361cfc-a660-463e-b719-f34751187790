<template>
  <div :class="$style.organizationPage">
    <!-- 动态组件切换 -->
    <component :is="currentComponent" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import LeadershipIndex from './leadership/index.vue';
import OfficeIndex from './office/index.vue';
import ConstitutionIndex from './constitution/index.vue';
import ManagementIndex from './management/index.vue';

interface Props {
  activeSubTab?: string;
}

const props = withDefaults(defineProps<Props>(), {
  activeSubTab: 'leadership',
});

// 组件映射关系
const componentMap = {
  leadership: LeadershipIndex,
  office: OfficeIndex,
  constitution: ConstitutionIndex,
  management: ManagementIndex,
};

// 当前激活的组件
const currentComponent = computed(() => {
  return componentMap[props.activeSubTab as keyof typeof componentMap] || LeadershipIndex;
});

defineOptions({ name: 'OrganizationIndex' });
</script>

<style module lang="scss">
.organizationPage {
  width: 100%;
}
</style>
