<template>
  <div style="width: 100%">
    <!-- 页面头部导航 -->
    <ComPortalPageHeader
      page-title="公共安全科学技术学会"
      :tabs="tabs"
      :sub-tabs="currentSubTabs"
      :breadcrumbs="breadcrumbs"
      :default-tab="activeTab"
      :default-sub-tab="activeSubTab"
      @tab-change="handleTabChange"
      @sub-tab-change="handleSubTabChange"
    />

    <!-- 主体内容区域 -->
    <div class="com-pc-portal-container">
      <div :class="$style.contentWrapper">
        <!-- 动态组件切换 -->
        <component :is="currentComponent" :active-sub-tab="activeSubTab" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import ComPortalPageHeader, { type ITab, type ISubTab } from '@/views/pc-portal/common/comp/ComPortalPageHeader.vue';
import { type IBreadData } from '@/views/pc-portal/common/comp/ComBread.vue';
import IntroductionTab from './introduction/index.vue';
import OrganizationWrapper from './organization/OrganizationWrapper.vue';
import LearnCharter from './leadership/index.vue';
import HistoryTab from './history/index.vue';

const activeTab = ref('introduction');
const activeSubTab = ref('leadership');

// Tab配置
const tabs = ref<ITab[]>([
  { name: 'introduction', label: '学会简介' },
  { name: 'organization', label: '组织架构' },
  { name: 'LearnCharter', label: '学会章程' },
  { name: 'history', label: '发展历程' },
]);

// 面包屑配置
const breadcrumbs = ref<IBreadData[]>([
  { name: '学会概况' },
  { name: '学会简介' }, // 这个会根据activeTab动态更新
]);

// 组织架构的二级菜单配置
const organizationSubTabs = ref<ISubTab[]>([
  { name: 'leadership', label: '学会领导' },
  { name: 'office', label: '办事机构' },
  { name: 'constitution', label: '学会分支机构' },
  { name: 'management', label: '理事单位' },
]);

// 当前二级菜单（只有组织架构有二级菜单）
const currentSubTabs = computed(() => {
  return activeTab.value === 'organization' ? organizationSubTabs.value : [];
});

// 组件映射关系
const componentMap = {
  introduction: IntroductionTab,
  organization: OrganizationWrapper,
  LearnCharter: LearnCharter,
  history: HistoryTab,
};

// 当前激活的组件
const currentComponent = computed(() => {
  return componentMap[activeTab.value as keyof typeof componentMap] || IntroductionTab;
});

function handleTabChange(value: string, tab: ITab) {
  activeTab.value = value;
  // 切换到组织架构时，重置二级菜单
  if (value === 'organization') {
    activeSubTab.value = 'leadership';
  }
  console.log('Tab changed to:', tab.label);
}

function handleSubTabChange(value: string, subTab: ISubTab) {
  activeSubTab.value = value;
  console.log('SubTab changed to:', subTab.label);
}

defineOptions({ name: 'OverviewIntroduction' });
</script>

<style module lang="scss">
.contentWrapper {
  padding: 5px;
  background: #f2f8fc;
  border-radius: 8px;
}
</style>
