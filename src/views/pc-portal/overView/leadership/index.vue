<template>
  <div :class="$style.leadershipPage">
    <div class="relative">
      <n-flex class="absolute left-0 top-0 bg-green-200" vertical>
        <n-button text>章程一</n-button>
        <n-button text>章程二</n-button>
        <n-button text>章程三</n-button>
      </n-flex>

      <div class="pl-[100px]">
        <n-scrollbar style="max-height: calc(100vh - 108px - 72px)">
          <div>
            <section id="part1">
              <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Illum, omnis?</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>

            <section id="part2">
              <p>Illum, omnis?</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>
          </div>
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'LeadershipIndex' });
</script>

<style module lang="scss">
.leadershipPage {
  width: 100%;
  min-height: 600px;
}
</style>
