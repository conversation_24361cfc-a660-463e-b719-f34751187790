<template>
  <div :class="$style.leadershipPage">
    <div class="relative">
      <n-flex class="absolute left-0 top-0 bg-green-200" vertical>
        <n-button text @click="scrollToSection('part1')">章程一</n-button>
        <n-button text @click="scrollToSection('part2')">章程二</n-button>
        <n-button text @click="scrollToSection('part3')">章程三</n-button>
      </n-flex>

      <div class="pl-[100px]">
        <n-scrollbar ref="scrollbarRef" style="max-height: calc(100vh - 108px - 72px)">
          <div>
            <section id="part1">
              <h2>章程一</h2>
              <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Illum, omnis?</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>

            <section id="part2">
              <h2>章程二</h2>
              <p>Illum, omnis?</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>

            <section id="part3">
              <h2>章程三</h2>
              <p>这是第三个章程的内容</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>
          </div>
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { ScrollbarInst } from 'naive-ui';

defineOptions({ name: 'LeadershipIndex' });

const scrollbarRef = ref<ScrollbarInst>();

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId);
  if (element && scrollbarRef.value) {
    // 获取元素相对于滚动容器的位置
    const scrollContainer = scrollbarRef.value.containerRef;
    if (scrollContainer) {
      const elementTop = element.offsetTop;
      scrollbarRef.value.scrollTo({ top: elementTop, behavior: 'smooth' });
    }
  }
};
</script>

<style module lang="scss">
.leadershipPage {
  width: 100%;
  min-height: 600px;
}
</style>
