<template>
  <div :class="$style.leadershipPage">
    <div class="relative">
      <n-flex class="absolute left-0 top-0 bg-green-200" vertical>
        <n-button text @click="scrollToSection('part1')">章程一</n-button>
        <n-button text @click="scrollToSection('part2')">章程二</n-button>
        <n-button text @click="scrollToSection('part3')">章程三</n-button>
      </n-flex>

      <div class="pl-[100px]">
        <n-scrollbar ref="scrollbarRef" style="max-height: calc(100vh - 108px - 72px)">
          <div>
            <section id="part1">
              <h2>章程一</h2>
              <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Illum, omnis?</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>

            <section id="part2">
              <h2>章程二</h2>
              <p>Illum, omnis?</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>

            <section id="part3">
              <h2>章程三</h2>
              <p>这是第三个章程的内容</p>
              <div style="height: 1000px">
                Lorem ipsum dolor sit amet, consectetur adipisicing elit. A aliquid amet deleniti distinctio, dolorem
                error impedit ipsum maiores placeat praesentium quaerat quia similique suscipit tenetur vel velit,
                voluptate? Dignissimos, officia.
              </div>
            </section>
          </div>
        </n-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { ScrollbarInst } from 'naive-ui';

defineOptions({ name: 'LeadershipIndex' });

const scrollbarRef = ref<ScrollbarInst>();

const scrollToSection = (sectionId: string) => {
  console.log('Scrolling to section:', sectionId); // 调试日志
  const element = document.getElementById(sectionId);
  console.log('Found element:', element); // 调试日志

  if (element && scrollbarRef.value) {
    // 获取滚动容器
    const scrollContainer = scrollbarRef.value.containerRef;
    console.log('Scroll container:', scrollContainer); // 调试日志

    if (scrollContainer) {
      // 计算元素相对于滚动容器的位置
      const containerRect = scrollContainer.getBoundingClientRect();
      const elementRect = element.getBoundingClientRect();
      const scrollTop = scrollContainer.scrollTop;

      // 计算目标滚动位置
      const targetScrollTop = scrollTop + (elementRect.top - containerRect.top);

      console.log('Target scroll position:', targetScrollTop); // 调试日志

      // 使用 Naive UI 的 scrollTo 方法
      scrollbarRef.value.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      });
    }
  }
};
</script>

<style module lang="scss">
.leadershipPage {
  width: 100%;
  min-height: 600px;
}
</style>
