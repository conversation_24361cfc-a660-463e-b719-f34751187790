<template>
  <div :class="$style.pageHeaderWrap">
    <!-- 主标题和主导航区域 -->
    <div class="flex items-center justify-between h-[64px] w-[87.5rem] mx-auto">
      <div class="text-[#0C87E5FF] text-[20px] font-semibold">{{ pageTitle || '公共安全科学技术学会' }}</div>

      <!-- Tab导航 (可选) -->
      <div v-if="tabs && tabs.length > 0">
        <n-tabs type="bar" v-model:value="activeTab" @update:value="handleTabChange">
          <n-tab v-for="tab in tabs" :key="tab.name" :name="tab.name">{{ tab.label }}</n-tab>
        </n-tabs>
      </div>
    </div>

    <!-- 二级菜单 (可选) - 独立区域 -->
    <div class="flex items-center justify-between h-[64px] w-[87.5rem] mx-auto" v-if="subTabs && subTabs.length > 0">
      <div class="text-[#0C87E5FF] text-[20px] font-semibold"></div>

      <div :class="$style.subTabsWrap">
        <div class="mx-auto">
          <n-tabs type="bar" v-model:value="activeSubTab" @update:value="handleSubTabChange" :class="$style.subTabs">
            <n-tab v-for="subTab in subTabs" :key="subTab.name" :name="subTab.name">{{ subTab.label }}</n-tab>
          </n-tabs>
        </div>
      </div>
    </div>
  </div>
  <!-- 面包屑导航 -->
  <div class="w-[87.5rem] mx-auto bg-[#F2F8FC]">
    <ComBread :data="breadcrumbData" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import ComBread, { type IBreadData } from './ComBread.vue';

export interface ITab {
  name: string;
  label: string;
}

export interface ISubTab {
  name: string;
  label: string;
}

interface Props {
  pageTitle?: string;
  tabs?: ITab[];
  subTabs?: ISubTab[];
  breadcrumbs: IBreadData[];
  defaultTab?: string;
  defaultSubTab?: string;
}

const props = withDefaults(defineProps<Props>(), {
  pageTitle: '公共安全科学技术学会',
  tabs: () => [],
  subTabs: () => [],
  breadcrumbs: () => [],
  defaultTab: '',
  defaultSubTab: '',
});

const emit = defineEmits<{
  tabChange: [value: string, tab: ITab];
  subTabChange: [value: string, subTab: ISubTab];
}>();

const activeTab = ref(props.defaultTab || (props.tabs?.[0]?.name ?? ''));
const activeSubTab = ref(props.defaultSubTab || (props.subTabs?.[0]?.name ?? ''));

// 计算面包屑数据，如果有tabs则动态更新
const breadcrumbData = computed(() => {
  if (props.tabs && props.tabs.length > 0 && activeTab.value) {
    const currentTab = props.tabs.find((tab) => tab.name === activeTab.value);
    if (currentTab) {
      // 复制原有面包屑，并更新最后一项为当前tab
      const breadcrumbs = [...props.breadcrumbs];
      if (breadcrumbs.length > 0) {
        breadcrumbs[breadcrumbs.length - 1] = {
          name: currentTab.label,
        };
      }
      return breadcrumbs;
    }
  }
  return props.breadcrumbs;
});

function handleTabChange(value: string) {
  const currentTab = props.tabs.find((tab) => tab.name === value);
  if (currentTab) {
    emit('tabChange', value, currentTab);
  }
}

function handleSubTabChange(value: string) {
  const currentSubTab = props.subTabs.find((subTab) => subTab.name === value);
  if (currentSubTab) {
    emit('subTabChange', value, currentSubTab);
  }
}

// 监听props变化
watch(
  () => props.defaultTab,
  (newVal) => {
    if (newVal) {
      activeTab.value = newVal;
    }
  }
);

watch(
  () => props.defaultSubTab,
  (newVal) => {
    if (newVal) {
      activeSubTab.value = newVal;
    }
  }
);

defineOptions({ name: 'ComPortalPageHeader' });
</script>

<style module lang="scss">
.pageHeaderWrap {
  width: 100%;
  background: #ffffff;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  border-radius: 0px 0px 0px 0px;

  .subTabsWrap {
    background: #f8fafc;
    padding: 0.75rem 0;

    .subTabs {
      :global(.n-tabs-nav) {
        --n-tab-text-color: #666;
        --n-tab-text-color-active: #0c87e5;
        --n-tab-text-color-hover: #0c87e5;
        --n-bar-color: #0c87e5;
      }
    }
  }
}
</style>
