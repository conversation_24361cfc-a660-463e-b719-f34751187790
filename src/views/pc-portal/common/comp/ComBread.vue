<template>
  <n-breadcrumb :theme-overrides="breadcrumbTheme">
    <n-breadcrumb-item v-for="item of data" :clickable="!!item.clickable" :key="item.name" @click="handleClick(item)">
      {{ item.name }}
    </n-breadcrumb-item>
  </n-breadcrumb>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { IObj } from '@/types';
import { BreadcrumbProps } from 'naive-ui';
type BreadcrumbThemeOverrides = NonNullable<BreadcrumbProps['themeOverrides']>;
const breadcrumbTheme: BreadcrumbThemeOverrides = {
  fontSize: '14px',
  itemLineHeight: '24px',
  itemTextColor: '#999999FF',
  itemTextColorHover: '#4D4D4DFF',
  itemTextColorPressed: '#4D4D4DFF',
  itemColorPressed: '#4D4D4DFF',
  itemTextColorActive: '#4D4D4DFF',
  separatorColor: '#999999FF',
};

interface RouteRecordRaw {
  name?: string;
  path?: string;
  params?: IObj<any>;
  query?: IObj<any>;
}

export interface IBreadData {
  name: string;
  clickable?: boolean;
  routeRaw?: RouteRecordRaw;
}
interface Props {
  data: IBreadData[];
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
});
const router = useRouter();

/**
 * 路由跳转
 * @param item
 */
function handleClick(item: IBreadData) {
  if (item.clickable && item.routeRaw) {
    router.push(item.routeRaw);
  }
}

defineOptions({ name: 'ComBread' }); // 面包屑组件
</script>

<style module></style>
