<template>
  <div :class="[$style.ComPortalBox, hasBg ? $style.bg : '']">
    <ComPortalTitle :title="title" :more="more" :moreRouteName="moreRouteName" :has-more="hasMore">
      <template #title>
        <slot name="title"></slot>
      </template>
      <template #more>
        <slot name="more"></slot>
      </template>
    </ComPortalTitle>

    <div :class="$style.content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import ComPortalTitle from './ComPortalTitle.vue';

const props = defineProps({
  title: String,
  hasBg: {
    type: Boolean,
    default: false,
  },
  hasMore: {
    type: Boolean,
    default: true,
  },
  more: {
    type: String,
    required: false,
  },
  moreRouteName: {
    type: String,
    required: false,
  },
});

defineOptions({ name: 'ComPortalBox' });
</script>

<style module lang="scss">
.ComPortalBox {
  border-radius: 8px;

  &.bg {
    background: #fff;
    padding: 24px;
  }

  .content {
    min-height: 100px;
    padding-top: 20px;
  }
}
</style>
