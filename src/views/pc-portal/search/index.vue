<template>
  <div class="com-pc-portal-container">
    <ComBread :data="breadData" />

    <div v-if="dataList.length">todo</div>
    <ComEmpty v-else class="com-pc-portal-empty" />
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/views/pc-portal/common/comp/ComBread.vue';
import type { IBreadData } from '@/types';
import { ref } from 'vue';
import ComEmpty from '@/components/empty/index.vue';

const breadData: IBreadData[] = [{ name: '首页', routeRaw: { path: '/' }, clickable: true }, { name: '搜索结果' }];

const dataList = ref([]);

defineOptions({ name: 'PcPortalSearch' });
</script>

<style scoped lang="scss"></style>
