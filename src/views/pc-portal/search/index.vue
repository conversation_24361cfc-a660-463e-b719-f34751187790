<template>
  <div :class="$style.PcPortalSearch" class="com-pc-portal-container">
    <ComBread :data="breadData" />

    <div v-if="dataList.length">
      <div>
        <section v-for="item in dataList" :key="item.id" :class="$style.group" @click="handleClick(item)">
          <div :class="$style.title">
            {{ item.bt }}
          </div>
          <div :class="$style.time">{{ item.fbsj }}</div>
        </section>
      </div>
      <div class="flex justify-end mt-[24px]">
        <PaginationComp />
      </div>
    </div>
    <ComEmpty v-else class="com-pc-portal-empty" />
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/views/pc-portal/common/comp/ComBread.vue';
import ComEmpty from '@/components/empty/index.vue';
import type { IBreadData } from '@/types';
import { IPageItem } from './type.ts';
import { pageList } from './fetchData.ts';
import { useNaivePagination, useNPaginationComp } from '@/utils/useNaivePagination.ts';
import { useRouter } from 'vue-router';
import { computed, ref, watch } from 'vue';

const breadData: IBreadData[] = [{ name: '首页', routeRaw: { path: '/' }, clickable: true }, { name: '搜索结果' }];
const { pagination, updateTotal } = useNaivePagination(getData);
const PaginationComp = useNPaginationComp(pagination);

const router = useRouter();
const keyword = computed(() => router.currentRoute.value.query.keyword as string);
const dataList = ref<IPageItem[]>([]);

function handleClick(val: IPageItem) {
  // news/detail?id=86fb062411ee4c5bb8f56b647d0b839b 截取参数传给query
  if (val.redirectUrl) {
    const query = val.redirectUrl.split('?')[1];
    // 'id=86fb062411ee4c5bb8f56b647d0b839b' 转成参数对象用 new Search
    const params = new URLSearchParams(query);

    router.push({ path: val.redirectUrl });
  }
}

function getData() {
  pageList({
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    keyWord: keyword.value,
  }).then((res) => {
    dataList.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

watch(
  () => keyword.value,
  (val) => {
    if (val) {
      getData();
    }
  },
  { immediate: true }
);

defineOptions({ name: 'PcPortalSearch' });
</script>

<style module lang="scss">
.PcPortalSearch {
  min-height: 400px;

  .group {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    margin: 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 20px;
    cursor: pointer;
  }

  .title {
    font-size: 18px;
    font-weight: 600;
  }

  .time {
    font-size: 16px;
    color: #666;
    flex-shrink: 0;
  }
}
</style>
