<template>
  <div>
    <div class="com-pc-portal-container">
      <ComBread :data="breadData"></ComBread>
      <n-h1 class="text-center">{{ details.bt }}</n-h1>
      <div class="com-pc-portal-container-time">发布时间：{{ details.fbsj }}</div>
      <div v-if="details.tztFileAttachmentList && details.tztFileAttachmentList.length > 0" class="pdf-list">
        <div v-for="(file, index) in details.tztFileAttachmentList" :key="index" class="pdf-item">
          <img :src="getPahtUrl(file.filePath)" :alt="file.fileName" class="w-full h-full" />
        </div>
      </div>
      <div
        class="pt-[30px] pb-[20px]"
        v-if="details.tzfjFileAttachmentList && details.tzfjFileAttachmentList.length > 0"
      >
        <div v-for="(pdf, index) in details.tzfjFileAttachmentList" :key="index" class="flex items-center">
          <n-icon color="#0C87E5FF" :component="FlDocumentPdf" />
          <n-a :href="getPahtUrl(pdf.filePath)" :title="pdf.fileName" target="_blank">{{ pdf.fileName }}</n-a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import ComBread from '@/views/pc-portal/common/comp/ComBread.vue';
import { useRoute } from 'vue-router';
import { showNoticesDetailsAPI } from '../fetchData';
import { FlDocumentPdf } from '@kalimahapps/vue-icons';
// import { Base64 } from 'js-base64';
const route = useRoute();

const breadData = ref<any>([
  {
    name: '通知公告',
    clickable: true,
    routeRaw: {
      name: 'pcPortalNoticeIndex',
    },
  },
  {
    name: '详情',
  },
]);
// function base64Encode(str: string): string {
//   return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))));
// }
// 图片地址
function getPahtUrl(val: string) {
  // const onlinePreview = 'http://**************:9092/onlinePreview?url=';
  return window.$SYS_CFG.apiBaseFile + val;
}
// function getOnlinePahtUrl(val: string) {
//   const onlinePreview = 'http://**************:9092/onlinePreview?url=';
//   return onlinePreview + encodeURIComponent(base64Encode(window.$SYS_CFG.apiBaseFile + val));
// }

const details = ref<any>({});
function getDetails() {
  showNoticesDetailsAPI({
    id: route.query.id,
  }).then((res: any) => {
    details.value = res.data;
  });
}

onMounted(() => {
  getDetails();
});

defineOptions({ name: 'NoticeDetails' });
</script>

<style module lang="scss"></style>
