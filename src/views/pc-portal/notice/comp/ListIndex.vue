<template>
  <div class="com-pc-portal-container">
    <n-spin :show="loading">
      <div v-if="tableData.length > 0">
        <div :class="$style.cardWrap" v-for="(item, index) in tableData" :key="index" @click="handleClick(item)">
          <div class="min-w-[42px] h-[42px] mr-[24px] bg-[#0C87E51F] rounded-[50%] flex items-center justify-center">
            <n-icon color="#0C87E5FF" :component="BsBellFill" />
          </div>
          <div class="w-full">
            <div class="flex justify-between">
              <div class="text-[18px] font-[600] mb-[8px]">{{ item.bt }}</div>
              <div class="text-[16px] text-[#666666FF]">{{ item.fbsj }}</div>
            </div>
            <div class="text-[14px] font-[400] mb-[8px] text-[#666666FF]">
              {{ item.fbt && item.fbt.length > 100 ? item.fbt.slice(0, 100) + '...' : item.fbt }}
            </div>
          </div>
        </div>
      </div>
      <div class="text-center h-[285px]" v-else>
        <ComEmpty />
      </div>
    </n-spin>
    <PaginationComp class="justify-end pt-[10px]" />
  </div>
</template>

<script setup lang="ts">
// import { useAutoLoading } from '@/common/hooks/useAutoLoading';
import { useNaivePagination, useNPaginationComp } from '@/utils/useNaivePagination.ts';
import { onMounted, ref } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { IObj } from '@/types';
import { BsBellFill } from '@kalimahapps/vue-icons';
import { useRouter } from 'vue-router';
import { showNoticesAPI } from '../fetchData';
import ComEmpty from '@/components/empty/index.vue';

// const show = ref(false);
const router = useRouter();
const { pagination, updateTotal } = useNaivePagination(getTableData);
const PaginationComp = useNPaginationComp(pagination);
pagination.pageSize = 3;

const [loading, search] = useAutoLoading(true);
let filterData: IObj<any> = {}; // 搜索条件

const tableData = ref<any[]>([]);
function getTableData() {
  const params = { ...filterData, pageNo: pagination.page, pageSize: pagination.pageSize };
  console.log(params, '-=-=-=-=-');
  search(
    showNoticesAPI(params).then((res: any) => {
      tableData.value = res.data?.rows || [];
      updateTotal(res.data?.total || 0);
    })
  );
}

function handleClick(item: IObj<any>) {
  console.log(item, '-=-=-=-=-');
  router.push({
    name: 'pcPortalNoticeDetails',
    query: {
      id: item.id,
    },
  });
}
onMounted(() => {
  getTableData();
});

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

defineExpose({
  getTableDataWrap,
});
defineOptions({ name: 'NoticeListIndex' });
</script>

<style module lang="scss">
.cardWrap {
  // width: 1400px;
  height: 106px;
  background: #ffffff;
  border-radius: 8px 8px 8px 8px;
  display: flex;
  padding: 24px;
  margin: 24px 0;
  align-items: center;
}
</style>
