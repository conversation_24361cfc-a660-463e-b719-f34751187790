<template>
  <div style="width: 100%">
    <!-- <div :class="$style.learndynamicheaderWrap">
      <div class="flex items-center justify-between h-[64px] w-[87.5rem] mx-auto">
        <div class="text-[#0C87E5FF] text-[20px] font-semibold">公共安全科学技术学会</div>
        <div>
          <n-tabs type="bar" @update:value="handleUpdateValue" v-model:value="tabName">
            <n-tab v-for="tab in tabs" :key="tab.name" :name="tab.name"> {{ tab.label }} </n-tab>
          </n-tabs>
        </div>
      </div>
      <div class="w-[87.5rem] mx-auto"><ComBread :data="breadData"></ComBread></div>
    </div> -->
    <div class="com-pc-portal-container">
      <ComBread :data="breadData"></ComBread>
      <ListIndex ref="tableRef"></ListIndex>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComBread from '@/views/pc-portal/common/comp/ComBread.vue';
import ListIndex from './comp/ListIndex.vue';

const tabName = ref('1');
const tableRef = ref();

const tabs = ref([
  { name: '0', label: '公告类型1' },
  { name: '1', label: '公告类型2' },
  { name: '2', label: '公告类型3' },
  { name: '3', label: '公告类型4' },
  { name: '4', label: '公告类型5' },
  { name: '5', label: '公告类型6' },
]);
const breadData = ref<any>([
  { name: '通知公告' },
  // {
  //   name: tabs.value[+tabName.value].label,
  // },
]);
function handleUpdateValue(val: string) {
  tabName.value = val;
  breadData.value = [
    { name: '通知公告' },
    {
      name: tabs.value[+tabName.value].label,
    },
  ];
  tableRef.value.getTableDataWrap({ type: +tabName.value });
}

defineOptions({ name: 'NoticeIndex' });
</script>

<style module lang="scss">
.learndynamicheaderWrap {
  width: 100%;
  height: 64px;
  background: #ffffff;
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.06);
  border-radius: 0px 0px 0px 0px;
}
</style>
