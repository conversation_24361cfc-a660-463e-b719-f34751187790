<template>
  <div :class="$style['page-login']">
    <div class="com-g-row-a1 relative" :class="$style['login-form']">
      <div :class="$style['logo']"></div>
      <LoginForm />
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginForm from './LoginForm.vue';

defineOptions({ name: 'PcLoginIndex' });
</script>

<style module lang="scss">
.page-login {
  position: relative;
  overflow: hidden;
  height: 100vh;
  min-width: 1600px;
  background: url('@/assets/login-bg.png') no-repeat center;
  background-size: 100%;

  .login-form {
    width: 450px;
    height: 530px;
    position: absolute;
    left: 50%;
    top: 50%;
    padding-top: 70px;
    transform: translate(-50%, -50%);
    background: url('@/assets/login-bg.png') no-repeat center;
    background-size: cover;
    border-radius: 8px;

    .logo {
      width: 100px;
      height: 100px;
      margin: 0 auto 36px;
      background: url('@/assets/log.png') no-repeat center;
      background-size: contain;
    }
  }
}
</style>
