import { useState } from '@/common/hooks/useState.ts';
import { IDict } from '@/types';
import { $dict } from './dict.ts';

export const dictHooks = {
  // 发布状态
  useFbzt() {
    const [opt, setOpt] = useState<IDict[]>([]);

    $dict.getSysDictByType('fbzt').then(({ data }) => {
      setOpt(data || []);
    });
    return { fbztOpt: opt };
  },
  // 角色状态
  useJszt() {
    const [opt, setOpt] = useState<IDict[]>([]);

    $dict.getSysDictByType('jszt').then(({ data }) => {
      setOpt(data || []);
    });
    return { jsztOpt: opt };
  },
  // 账号状态
  useZhzt() {
    const [opt, setOpt] = useState<IDict[]>([]);

    $dict.getSysDictByType('zhzt').then(({ data }) => {
      setOpt(data || []);
    });
    return { zhztOpt: opt };
  },
  // 单位类型
  useDwlx() {
    const [opt, setOpt] = useState<IDict[]>([]);

    $dict.getSysDictByType('dwlx').then(({ data }) => {
      setOpt(data || []);
    });
    return { dwlxOpt: opt };
  },
  // 学会动态类型
  useXhdtlx() {
    const [opt, setOpt] = useState<IDict[]>([]);

    $dict.getSysDictByType('dtlx').then(({ data }) => {
      setOpt(data || []);
    });
    return { xhdtlxOpt: opt };
  },
  // 相关链接类型
  useXgljlx() {
    const [opt, setOpt] = useState<IDict[]>([]);

    $dict.getSysDictByType('xgljlx').then(({ data }) => {
      setOpt(data || []);
    });
    return { xgljlxOpt: opt };
  },
};
